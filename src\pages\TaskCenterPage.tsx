import React, { useState } from 'react'
import { <PERSON>, Row, Col, Typo<PERSON>, Button, Table, Tag, Space, Modal, Form, Input, Select, DatePicker, Steps, Collapse, Timeline, Divider } from 'antd'
import { useNavigate } from 'react-router-dom'
import {
  PlusOutlined,
  EyeOutlined,
  Play<PERSON>ircleOutlined,
  <PERSON><PERSON><PERSON>cleOutlined,
  Clock<PERSON>ircleOutlined,
  FileTextOutlined,
  BulbOutlined,
  TeamOutlined,
  ToolOutlined,
  SearchOutlined,
  BookOutlined,
  TargetOutlined,
} from '@ant-design/icons'
import { useAuthStore } from '@/store/authStore'
import type { ColumnsType } from 'antd/es/table'

const { Title, Paragraph, Text } = Typography
const { Option } = Select
const { TextArea } = Input
const { Panel } = Collapse
const { Step } = Steps

interface Task {
  id: string
  title: string
  description: string
  type: 'individual' | 'team'
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedHours: number
  dueDate: string
  status: 'published' | 'in_progress' | 'submitted' | 'completed'
  course: string
  instructor: string
  requirements: string[]
  deliverables: string[]
  resources: Resource[]
  standards: Standard[]
  keyPoints: string[]
  studentCount?: number
  submittedCount?: number
}

interface Resource {
  id: string
  name: string
  type: 'video' | 'document' | 'link' | 'material'
  url: string
  description: string
}

interface Standard {
  id: string
  category: string
  description: string
  weight: number
}

interface TaskAnalysis {
  taskId: string
  keyElements: string[]
  difficulties: string[]
  solutions: string[]
  timeline: TimelineItem[]
}

interface TimelineItem {
  phase: string
  duration: string
  activities: string[]
  deliverables: string[]
}

const TaskCenterPage: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAuthStore()
  const [activeTab, setActiveTab] = useState('available')
  const [selectedTask, setSelectedTask] = useState<Task | null>(null)
  const [isTaskModalVisible, setIsTaskModalVisible] = useState(false)
  const [isAnalysisModalVisible, setIsAnalysisModalVisible] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)

  // 模拟任务数据
  const [tasks] = useState<Task[]>([
    {
      id: '1',
      title: '汽车展展位设计',
      description: '为2024春季汽车展设计一个创新的展位，要求体现科技感和未来感，面积不超过100平方米',
      type: 'individual',
      difficulty: 'intermediate',
      estimatedHours: 40,
      dueDate: '2024-02-15',
      status: 'published',
      course: '会展策划基础',
      instructor: '张教授',
      requirements: [
        '展位面积控制在100平方米以内',
        '体现汽车品牌的科技感和未来感',
        '包含产品展示区、洽谈区、休息区',
        '考虑人流动线和安全因素',
        '预算控制在50万元以内'
      ],
      deliverables: [
        '3D设计方案文件',
        '平面布局图',
        '材料清单和成本预算',
        '设计说明书',
        '效果图渲染'
      ],
      resources: [
        { id: '1', name: '汽车展设计案例', type: 'video', url: '/videos/case1.mp4', description: '优秀汽车展设计案例分析' },
        { id: '2', name: '展位设计规范', type: 'document', url: '/docs/standards.pdf', description: '行业标准和设计规范' },
        { id: '3', name: '材料库', type: 'link', url: '/projects/materials', description: '可用材料和价格信息' }
      ],
      standards: [
        { id: '1', category: '创新性', description: '设计理念新颖，具有创新元素', weight: 25 },
        { id: '2', category: '实用性', description: '布局合理，功能完善', weight: 25 },
        { id: '3', category: '美观性', description: '视觉效果佳，符合品牌形象', weight: 20 },
        { id: '4', category: '经济性', description: '成本控制合理，性价比高', weight: 20 },
        { id: '5', category: '安全性', description: '符合安全规范，考虑人员安全', weight: 10 }
      ],
      keyPoints: [
        '理解汽车品牌定位和目标客户',
        '掌握展位空间规划原理',
        '熟悉材料特性和成本控制',
        '注重用户体验和互动设计'
      ]
    },
    {
      id: '2',
      title: '科技会议展示设计',
      description: '为科技创新大会设计会议展示空间，突出科技创新主题',
      type: 'team',
      difficulty: 'advanced',
      estimatedHours: 60,
      dueDate: '2024-03-01',
      status: 'published',
      course: '展示设计原理',
      instructor: '李教授',
      requirements: [
        '团队协作完成（3-4人）',
        '体现科技创新主题',
        '包含多媒体展示区域',
        '考虑会议流程和人员动线'
      ],
      deliverables: [
        '团队设计方案',
        '分工计划书',
        '3D场景文件',
        '团队协作报告'
      ],
      resources: [
        { id: '4', name: '科技展示案例', type: 'video', url: '/videos/tech.mp4', description: '科技主题展示设计' },
        { id: '5', name: '团队协作指南', type: 'document', url: '/docs/teamwork.pdf', description: '团队项目管理方法' }
      ],
      standards: [
        { id: '6', category: '主题表达', description: '准确表达科技创新主题', weight: 30 },
        { id: '7', category: '团队协作', description: '团队分工合理，协作有效', weight: 25 },
        { id: '8', category: '技术应用', description: '合理运用多媒体技术', weight: 25 },
        { id: '9', category: '整体效果', description: '整体设计协调统一', weight: 20 }
      ],
      keyPoints: [
        '团队沟通和协作技巧',
        '科技主题的视觉表达',
        '多媒体技术的合理运用',
        '会议空间的功能规划'
      ],
      studentCount: 32,
      submittedCount: 8
    }
  ])

  // 模拟任务分析数据
  const [taskAnalysis] = useState<TaskAnalysis>({
    taskId: '1',
    keyElements: [
      '空间规划与布局',
      '品牌形象表达',
      '材料选择与成本控制',
      '用户体验设计',
      '安全与规范要求'
    ],
    difficulties: [
      '在有限空间内实现功能最大化',
      '平衡美观性与实用性',
      '控制成本的同时保证质量',
      '创新设计与规范要求的平衡'
    ],
    solutions: [
      '运用空间设计原理，合理规划功能区域',
      '深入研究品牌特色，提炼设计元素',
      '充分利用材料库，进行成本对比分析',
      '参考优秀案例，借鉴成功经验'
    ],
    timeline: [
      {
        phase: '资讯阶段',
        duration: '2天',
        activities: ['任务分析', '资料收集', '案例研究'],
        deliverables: ['任务分析报告', '参考资料整理']
      },
      {
        phase: '计划阶段',
        duration: '3天',
        activities: ['方案构思', '计划制定', '资源规划'],
        deliverables: ['设计计划书', '时间安排表']
      },
      {
        phase: '决策阶段',
        duration: '2天',
        activities: ['方案比较', '专家咨询', '方案确定'],
        deliverables: ['方案选择报告']
      },
      {
        phase: '实施阶段',
        duration: '5天',
        activities: ['3D建模', '材料配置', '效果渲染'],
        deliverables: ['3D模型文件', '效果图']
      },
      {
        phase: '检查阶段',
        duration: '1天',
        activities: ['自检', '互检', '标准对照'],
        deliverables: ['检查报告']
      },
      {
        phase: '评估阶段',
        duration: '1天',
        activities: ['成果提交', '自我评估', '反思总结'],
        deliverables: ['最终作品', '反思报告']
      }
    ]
  })

  const taskColumns: ColumnsType<Task> = [
    {
      title: '任务信息',
      key: 'taskInfo',
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Text strong style={{ cursor: 'pointer' }} onClick={() => handleViewTask(record)}>
            {record.title}
          </Text>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.course} - {record.instructor}
          </Text>
          <Space>
            <Tag color={record.type === 'individual' ? 'blue' : 'green'}>
              {record.type === 'individual' ? '个人任务' : '团队任务'}
            </Tag>
            <Tag color={
              record.difficulty === 'beginner' ? 'green' :
              record.difficulty === 'intermediate' ? 'orange' : 'red'
            }>
              {record.difficulty === 'beginner' ? '初级' :
               record.difficulty === 'intermediate' ? '中级' : '高级'}
            </Tag>
          </Space>
        </Space>
      )
    },
    {
      title: '预估工时',
      dataIndex: 'estimatedHours',
      key: 'estimatedHours',
      render: (hours) => `${hours}小时`
    },
    {
      title: '截止时间',
      dataIndex: 'dueDate',
      key: 'dueDate'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={
          status === 'published' ? 'blue' :
          status === 'in_progress' ? 'orange' :
          status === 'submitted' ? 'purple' : 'green'
        }>
          {status === 'published' ? '可接收' :
           status === 'in_progress' ? '进行中' :
           status === 'submitted' ? '已提交' : '已完成'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button 
            type="link" 
            icon={<EyeOutlined />}
            onClick={() => handleViewTask(record)}
          >
            查看详情
          </Button>
          {record.status === 'published' && (
            <Button 
              type="primary" 
              size="small"
              icon={<PlayCircleOutlined />}
              onClick={() => handleStartTask(record)}
            >
              开始任务
            </Button>
          )}
        </Space>
      )
    }
  ]

  const handleViewTask = (task: Task) => {
    setSelectedTask(task)
    setIsTaskModalVisible(true)
  }

  const handleStartTask = (task: Task) => {
    setSelectedTask(task)
    setIsAnalysisModalVisible(true)
    setCurrentStep(0)
  }

  const handleNextStep = () => {
    if (currentStep < 5) {
      setCurrentStep(currentStep + 1)
    } else {
      // 进入3D编辑器开始实施
      navigate('/design-3d')
    }
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>任务中心</Title>
        <Paragraph>
          基于六步教学法的任务管理中心，引导完整的学习过程
        </Paragraph>
      </div>

      {/* 六步教学法指引 */}
      <Card style={{ marginBottom: 24 }}>
        <Steps current={-1} size="small">
          <Step title="资讯" description="获取信息，明确任务" icon={<SearchOutlined />} />
          <Step title="计划" description="制定工作计划" icon={<FileTextOutlined />} />
          <Step title="决策" description="选择最佳方案" icon={<BulbOutlined />} />
          <Step title="实施" description="执行工作任务" icon={<ToolOutlined />} />
          <Step title="检查" description="检验工作成果" icon={<CheckCircleOutlined />} />
          <Step title="评估" description="总结反思提升" icon={<TargetOutlined />} />
        </Steps>
      </Card>

      {/* 任务列表 */}
      <Card title="可用任务">
        <Table
          columns={taskColumns}
          dataSource={tasks}
          rowKey="id"
          pagination={{ pageSize: 10 }}
        />
      </Card>

      {/* 任务详情模态框 */}
      <Modal
        title="任务详情"
        open={isTaskModalVisible}
        onCancel={() => setIsTaskModalVisible(false)}
        width={800}
        footer={[
          <Button key="close" onClick={() => setIsTaskModalVisible(false)}>
            关闭
          </Button>,
          selectedTask?.status === 'published' && (
            <Button 
              key="start" 
              type="primary"
              onClick={() => {
                setIsTaskModalVisible(false)
                handleStartTask(selectedTask!)
              }}
            >
              开始任务
            </Button>
          )
        ]}
      >
        {selectedTask && (
          <div>
            <Collapse defaultActiveKey={['1', '2', '3']}>
              <Panel header="任务描述" key="1">
                <Paragraph>{selectedTask.description}</Paragraph>
              </Panel>
              
              <Panel header="任务要求" key="2">
                <ul>
                  {selectedTask.requirements.map((req, index) => (
                    <li key={index}>{req}</li>
                  ))}
                </ul>
              </Panel>
              
              <Panel header="交付物" key="3">
                <ul>
                  {selectedTask.deliverables.map((item, index) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
              </Panel>
              
              <Panel header="评估标准" key="4">
                {selectedTask.standards.map(standard => (
                  <div key={standard.id} style={{ marginBottom: 8 }}>
                    <Text strong>{standard.category}（{standard.weight}%）：</Text>
                    <Text>{standard.description}</Text>
                  </div>
                ))}
              </Panel>
              
              <Panel header="学习资源" key="5">
                {selectedTask.resources.map(resource => (
                  <div key={resource.id} style={{ marginBottom: 8 }}>
                    <Space>
                      <Tag color="blue">{resource.type}</Tag>
                      <a href={resource.url} target="_blank" rel="noopener noreferrer">
                        {resource.name}
                      </a>
                    </Space>
                    <div style={{ marginLeft: 16, fontSize: 12, color: '#666' }}>
                      {resource.description}
                    </div>
                  </div>
                ))}
              </Panel>
            </Collapse>
          </div>
        )}
      </Modal>

      {/* 任务分析引导模态框 */}
      <Modal
        title="任务分析引导"
        open={isAnalysisModalVisible}
        onCancel={() => setIsAnalysisModalVisible(false)}
        width={900}
        footer={[
          <Button key="prev" disabled={currentStep === 0} onClick={() => setCurrentStep(currentStep - 1)}>
            上一步
          </Button>,
          <Button key="next" type="primary" onClick={handleNextStep}>
            {currentStep < 5 ? '下一步' : '开始实施'}
          </Button>
        ]}
      >
        <Steps current={currentStep} style={{ marginBottom: 24 }}>
          <Step title="任务分析" />
          <Step title="关键要素" />
          <Step title="难点识别" />
          <Step title="解决方案" />
          <Step title="时间规划" />
          <Step title="开始实施" />
        </Steps>

        <div style={{ minHeight: 300 }}>
          {currentStep === 0 && (
            <div>
              <Title level={4}>任务分析</Title>
              <Paragraph>
                在开始任务之前，让我们先分析一下这个任务的核心要求和目标。
              </Paragraph>
              <Card>
                <Text strong>任务目标：</Text>
                <Paragraph>{selectedTask?.description}</Paragraph>
                <Text strong>关键要求：</Text>
                <ul>
                  {selectedTask?.requirements.slice(0, 3).map((req, index) => (
                    <li key={index}>{req}</li>
                  ))}
                </ul>
              </Card>
            </div>
          )}

          {currentStep === 1 && (
            <div>
              <Title level={4}>关键要素识别</Title>
              <Paragraph>
                识别完成这个任务需要掌握的关键要素：
              </Paragraph>
              <Row gutter={16}>
                {taskAnalysis.keyElements.map((element, index) => (
                  <Col span={12} key={index} style={{ marginBottom: 8 }}>
                    <Card size="small">
                      <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
                      {element}
                    </Card>
                  </Col>
                ))}
              </Row>
            </div>
          )}

          {currentStep === 2 && (
            <div>
              <Title level={4}>难点识别</Title>
              <Paragraph>
                预期可能遇到的困难和挑战：
              </Paragraph>
              {taskAnalysis.difficulties.map((difficulty, index) => (
                <Card key={index} size="small" style={{ marginBottom: 8 }}>
                  <Text type="warning">⚠️ {difficulty}</Text>
                </Card>
              ))}
            </div>
          )}

          {currentStep === 3 && (
            <div>
              <Title level={4}>解决方案建议</Title>
              <Paragraph>
                针对识别的难点，建议的解决方案：
              </Paragraph>
              {taskAnalysis.solutions.map((solution, index) => (
                <Card key={index} size="small" style={{ marginBottom: 8 }}>
                  <Text type="success">💡 {solution}</Text>
                </Card>
              ))}
            </div>
          )}

          {currentStep === 4 && (
            <div>
              <Title level={4}>时间规划建议</Title>
              <Paragraph>
                基于六步教学法的时间安排建议：
              </Paragraph>
              <Timeline>
                {taskAnalysis.timeline.map((phase, index) => (
                  <Timeline.Item key={index}>
                    <Text strong>{phase.phase}</Text>
                    <Text type="secondary"> ({phase.duration})</Text>
                    <div style={{ marginTop: 4 }}>
                      <Text>主要活动：{phase.activities.join('、')}</Text>
                    </div>
                    <div style={{ marginTop: 2 }}>
                      <Text type="secondary">交付物：{phase.deliverables.join('、')}</Text>
                    </div>
                  </Timeline.Item>
                ))}
              </Timeline>
            </div>
          )}

          {currentStep === 5 && (
            <div>
              <Title level={4}>准备开始实施</Title>
              <Paragraph>
                恭喜！您已经完成了任务分析和计划制定。现在可以开始进入3D编辑器进行设计实施了。
              </Paragraph>
              <Card>
                <Text strong>接下来您将：</Text>
                <ul>
                  <li>进入3D编辑器开始设计</li>
                  <li>根据分析结果选择合适的材料</li>
                  <li>实时查看成本计算</li>
                  <li>记录设计过程和决策</li>
                </ul>
              </Card>
            </div>
          )}
        </div>
      </Modal>
    </div>
  )
}

export default TaskCenterPage
