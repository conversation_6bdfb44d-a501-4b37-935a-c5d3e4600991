import React, { useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { Layout } from 'antd'
import MainLayout from '@/components/layout/MainLayout'
import HomePage from '@/pages/HomePage'
import LoginPage from '@/pages/LoginPage'
import PortalPage from '@/pages/PortalPage'
import LearningPage from '@/pages/LearningPage'
import CourseDetailPage from '@/pages/CourseDetailPage'
import ProjectManagementPage from '@/pages/ProjectManagementPage'
import AdminPage from '@/pages/AdminPage'
import ProfilePage from '@/pages/ProfilePage'
import ContentSpacePage from '@/pages/ContentSpacePage'
import VREditorPage from '@/pages/VREditorPage'
import Editor3DPage from '@/pages/Editor3DPage'
import StudentCoursePage from '@/pages/StudentCoursePage'
import TeacherManagePage from '@/pages/TeacherManagePage'
import ProposalApprovalPage from '@/pages/ProposalApprovalPage'
import AIPlanPage from '@/pages/AIPlanPage'
import Render2DPage from '@/pages/Render2DPage'
import { useAuthStore } from '@/store/authStore'
import { useProjectStore } from '@/store/projectStore'
import { ROUTES } from '@/utils/constants'
import './App.css'

const { Content } = Layout

// 受保护的路由组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useAuthStore()
  return isAuthenticated ? <>{children}</> : <Navigate to={ROUTES.LOGIN} replace />
}

function App() {
  const { isAuthenticated, getCurrentUser } = useAuthStore()
  const { fetchStatistics } = useProjectStore()

  useEffect(() => {
    // 应用启动时检查用户登录状态
    if (isAuthenticated) {
      getCurrentUser()
      fetchStatistics()
    }
  }, [isAuthenticated, getCurrentUser, fetchStatistics])

  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path={ROUTES.LOGIN} element={<LoginPage />} />
          <Route
            path="/*"
            element={
              <ProtectedRoute>
                <MainLayout>
                  <Routes>
                    <Route path={ROUTES.HOME} element={<HomePage />} />
                    <Route path={ROUTES.PORTAL} element={<PortalPage />} />

                    {/* 教管学考平台 */}
                    <Route path={ROUTES.LEARNING} element={<LearningPage />} />
                    <Route path={ROUTES.LEARNING_COURSES} element={<StudentCoursePage />} />
                    <Route path="/learning/courses/:id" element={<CourseDetailPage />} />
                    <Route path={ROUTES.LEARNING_ASSIGNMENTS} element={<StudentCoursePage />} />
                    <Route path={ROUTES.LEARNING_EXAMS} element={<StudentCoursePage />} />

                    {/* 教师端路由 */}
                    <Route path={ROUTES.LEARNING_CLASSES} element={<TeacherManagePage />} />
                    <Route path="/learning/teacher/courses" element={<TeacherManagePage />} />
                    <Route path="/learning/teacher/assignments" element={<TeacherManagePage />} />
                    <Route path="/learning/teacher/analytics" element={<TeacherManagePage />} />

                    {/* 项目管理平台 */}
                    <Route path={ROUTES.PROJECTS} element={<ProjectManagementPage />} />
                    <Route path={ROUTES.PROJECT_OVERVIEW} element={<ProjectManagementPage />} />
                    <Route path={ROUTES.PROJECT_MANAGEMENT} element={<ProjectManagementPage />} />
                    <Route path={ROUTES.PROJECT_PROPOSAL} element={<ProposalApprovalPage />} />
                    <Route path={ROUTES.PROJECT_CONSTRUCTION} element={<ProjectManagementPage />} />

                    {/* 3D设计相关 */}
                    <Route path={ROUTES.AI_PLAN} element={<AIPlanPage />} />
                    <Route path={ROUTES.RENDER_2D} element={<Render2DPage />} />
                    <Route path={ROUTES.DESIGN_3D} element={<Editor3DPage />} />
                    <Route path={ROUTES.MODEL_3D} element={<Editor3DPage />} />

                    {/* VR系统 */}
                    <Route path={ROUTES.VR_EDITOR} element={<VREditorPage />} />
                    <Route path={ROUTES.VR_EXPERIENCE} element={<VREditorPage />} />

                    {/* 内容空间 */}
                    <Route path={ROUTES.CONTENT_SPACE} element={<ContentSpacePage />} />
                    <Route path={ROUTES.CONTENT_GALLERY} element={<ContentSpacePage />} />
                    <Route path={ROUTES.CONTENT_PUBLISH} element={<ContentSpacePage />} />

                    {/* 管理功能 */}
                    <Route path={ROUTES.ADMIN} element={<AdminPage />} />
                    <Route path={ROUTES.USER_MANAGEMENT} element={<AdminPage />} />
                    <Route path={ROUTES.ROLE_MANAGEMENT} element={<AdminPage />} />
                    <Route path={ROUTES.SYSTEM_SETTINGS} element={<AdminPage />} />

                    {/* 个人中心 */}
                    <Route path={ROUTES.PROFILE} element={<ProfilePage />} />

                    <Route path="*" element={<Navigate to={ROUTES.HOME} replace />} />
                  </Routes>
                </MainLayout>
              </ProtectedRoute>
            }
          />
        </Routes>
      </div>
    </Router>
  )
}

export default App
