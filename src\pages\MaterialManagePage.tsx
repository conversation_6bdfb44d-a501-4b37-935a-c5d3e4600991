import React, { useState } from 'react'
import { Card, Row, Col, Typography, Button, Table, Tag, Space, Modal, Form, Input, Select, InputNumber, Upload, message, Tabs, Statistic, Image } from 'antd'
import { useNavigate } from 'react-router-dom'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  UploadOutlined,
  DownloadOutlined,
  ShoppingCartOutlined,
  DollarOutlined,
  InboxOutlined,
  SearchOutlined,
  FilterOutlined,
} from '@ant-design/icons'
import { useAuthStore } from '@/store/authStore'
import type { ColumnsType } from 'antd/es/table'

const { Title, Paragraph, Text } = Typography
const { Option } = Select
const { TextArea } = Input
const { TabPane } = Tabs
const { Dragger } = Upload

interface Material {
  id: string
  name: string
  category: 'structure' | 'decoration' | 'lighting' | 'furniture' | 'fabric' | 'electronic'
  subcategory: string
  brand: string
  model: string
  specification: string
  unit: 'piece' | 'meter' | 'square_meter' | 'cubic_meter' | 'kilogram' | 'set'
  unitPrice: number
  currency: 'CNY' | 'USD' | 'EUR'
  supplier: string
  supplierContact: string
  leadTime: number // 交货周期（天）
  minOrderQty: number
  stockQty: number
  description: string
  images: string[]
  tags: string[]
  isActive: boolean
  createdAt: string
  updatedAt: string
  usageCount: number // 在3D编辑器中的使用次数
  totalCost: number // 总成本（使用次数 * 单价）
}

interface MaterialUsage {
  materialId: string
  materialName: string
  projectId: string
  projectName: string
  quantity: number
  unitPrice: number
  totalPrice: number
  usedAt: string
  editorSessionId: string
}

const MaterialManagePage: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAuthStore()
  const [activeTab, setActiveTab] = useState('materials')
  const [isMaterialModalVisible, setIsMaterialModalVisible] = useState(false)
  const [isImportModalVisible, setIsImportModalVisible] = useState(false)
  const [selectedMaterial, setSelectedMaterial] = useState<Material | null>(null)
  const [searchText, setSearchText] = useState('')
  const [categoryFilter, setCategoryFilter] = useState<string>('')
  const [materialForm] = Form.useForm()

  // 模拟材料库数据
  const [materials] = useState<Material[]>([
    {
      id: '1',
      name: '铝合金展台桁架',
      category: 'structure',
      subcategory: '桁架系统',
      brand: '展通',
      model: 'ZT-300',
      specification: '300x300x3000mm',
      unit: 'piece',
      unitPrice: 280,
      currency: 'CNY',
      supplier: '北京展通展具有限公司',
      supplierContact: '13800138001',
      leadTime: 7,
      minOrderQty: 10,
      stockQty: 150,
      description: '高强度铝合金材质，表面阳极氧化处理，承重能力强',
      images: ['/api/placeholder/300/200'],
      tags: ['铝合金', '桁架', '结构件'],
      isActive: true,
      createdAt: '2024-01-10',
      updatedAt: '2024-01-20',
      usageCount: 25,
      totalCost: 7000
    },
    {
      id: '2',
      name: 'LED射灯',
      category: 'lighting',
      subcategory: '射灯',
      brand: '飞利浦',
      model: 'PH-LED-50W',
      specification: '50W 3000K',
      unit: 'piece',
      unitPrice: 120,
      currency: 'CNY',
      supplier: '上海照明设备公司',
      supplierContact: '13900139001',
      leadTime: 3,
      minOrderQty: 5,
      stockQty: 80,
      description: '高亮度LED射灯，色温3000K，适用于展示照明',
      images: ['/api/placeholder/300/200'],
      tags: ['LED', '射灯', '照明'],
      isActive: true,
      createdAt: '2024-01-12',
      updatedAt: '2024-01-22',
      usageCount: 18,
      totalCost: 2160
    },
    {
      id: '3',
      name: '亚克力展示柜',
      category: 'furniture',
      subcategory: '展示柜',
      brand: '晶彩',
      model: 'JC-800',
      specification: '800x400x1200mm',
      unit: 'piece',
      unitPrice: 450,
      currency: 'CNY',
      supplier: '广州晶彩展具厂',
      supplierContact: '13700137001',
      leadTime: 10,
      minOrderQty: 2,
      stockQty: 30,
      description: '透明亚克力材质，内置LED灯带，适合产品展示',
      images: ['/api/placeholder/300/200'],
      tags: ['亚克力', '展示柜', '透明'],
      isActive: true,
      createdAt: '2024-01-15',
      updatedAt: '2024-01-25',
      usageCount: 12,
      totalCost: 5400
    }
  ])

  // 模拟材料使用记录
  const [materialUsages] = useState<MaterialUsage[]>([
    {
      materialId: '1',
      materialName: '铝合金展台桁架',
      projectId: 'proj-001',
      projectName: '2024春季汽车展',
      quantity: 8,
      unitPrice: 280,
      totalPrice: 2240,
      usedAt: '2024-01-20 14:30',
      editorSessionId: 'session-001'
    },
    {
      materialId: '2',
      materialName: 'LED射灯',
      projectId: 'proj-001',
      projectName: '2024春季汽车展',
      quantity: 6,
      unitPrice: 120,
      totalPrice: 720,
      usedAt: '2024-01-20 15:15',
      editorSessionId: 'session-001'
    },
    {
      materialId: '3',
      materialName: '亚克力展示柜',
      projectId: 'proj-002',
      projectName: '科技创新大会',
      quantity: 4,
      unitPrice: 450,
      totalPrice: 1800,
      usedAt: '2024-01-22 10:20',
      editorSessionId: 'session-002'
    }
  ])

  const materialColumns: ColumnsType<Material> = [
    {
      title: '材料信息',
      key: 'materialInfo',
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Text strong style={{ cursor: 'pointer' }} onClick={() => handleViewMaterial(record)}>
            {record.name}
          </Text>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.brand} {record.model}
          </Text>
          <Text type="secondary" style={{ fontSize: 12 }}>
            规格：{record.specification}
          </Text>
        </Space>
      ),
      width: 200
    },
    {
      title: '分类',
      key: 'category',
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Tag color="blue">{getCategoryText(record.category)}</Tag>
          <Text style={{ fontSize: 12 }}>{record.subcategory}</Text>
        </Space>
      )
    },
    {
      title: '价格信息',
      key: 'pricing',
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Text strong style={{ color: '#f5222d' }}>
            ¥{record.unitPrice.toFixed(2)}/{getUnitText(record.unit)}
          </Text>
          <Text style={{ fontSize: 12 }}>
            最小起订：{record.minOrderQty}{getUnitText(record.unit)}
          </Text>
        </Space>
      )
    },
    {
      title: '库存',
      dataIndex: 'stockQty',
      key: 'stockQty',
      render: (qty, record) => (
        <Space direction="vertical" size={0}>
          <Text style={{ color: qty > 50 ? '#52c41a' : qty > 10 ? '#faad14' : '#f5222d' }}>
            {qty} {getUnitText(record.unit)}
          </Text>
          <Text style={{ fontSize: 12 }}>
            交期：{record.leadTime}天
          </Text>
        </Space>
      )
    },
    {
      title: '使用情况',
      key: 'usage',
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Text>使用：{record.usageCount}次</Text>
          <Text strong style={{ color: '#722ed1' }}>
            总成本：¥{record.totalCost.toFixed(2)}
          </Text>
        </Space>
      )
    },
    {
      title: '供应商',
      key: 'supplier',
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Text>{record.supplier}</Text>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.supplierContact}
          </Text>
        </Space>
      )
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '停用'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button type="link" icon={<EyeOutlined />} onClick={() => handleViewMaterial(record)}>
            查看
          </Button>
          <Button type="link" icon={<EditOutlined />} onClick={() => handleEditMaterial(record)}>
            编辑
          </Button>
          <Button type="link" danger icon={<DeleteOutlined />}>
            删除
          </Button>
        </Space>
      )
    }
  ]

  const usageColumns: ColumnsType<MaterialUsage> = [
    { title: '材料名称', dataIndex: 'materialName', key: 'materialName' },
    { title: '项目名称', dataIndex: 'projectName', key: 'projectName' },
    { title: '使用数量', dataIndex: 'quantity', key: 'quantity' },
    { 
      title: '单价', 
      dataIndex: 'unitPrice', 
      key: 'unitPrice',
      render: (price) => `¥${price.toFixed(2)}`
    },
    { 
      title: '总价', 
      dataIndex: 'totalPrice', 
      key: 'totalPrice',
      render: (price) => <Text strong style={{ color: '#f5222d' }}>¥{price.toFixed(2)}</Text>
    },
    { title: '使用时间', dataIndex: 'usedAt', key: 'usedAt' },
    {
      title: '操作',
      key: 'action',
      render: () => (
        <Space>
          <Button type="link" icon={<EyeOutlined />}>查看详情</Button>
        </Space>
      )
    }
  ]

  const getCategoryText = (category: string) => {
    const categoryMap: Record<string, string> = {
      structure: '结构件',
      decoration: '装饰材料',
      lighting: '照明设备',
      furniture: '展具家具',
      fabric: '布艺材料',
      electronic: '电子设备'
    }
    return categoryMap[category] || category
  }

  const getUnitText = (unit: string) => {
    const unitMap: Record<string, string> = {
      piece: '件',
      meter: '米',
      square_meter: '平方米',
      cubic_meter: '立方米',
      kilogram: '公斤',
      set: '套'
    }
    return unitMap[unit] || unit
  }

  const handleViewMaterial = (material: Material) => {
    setSelectedMaterial(material)
    setIsMaterialModalVisible(true)
  }

  const handleEditMaterial = (material: Material) => {
    setSelectedMaterial(material)
    materialForm.setFieldsValue(material)
    setIsMaterialModalVisible(true)
  }

  const handleCreateMaterial = async (values: any) => {
    console.log('创建/编辑材料:', values)
    setIsMaterialModalVisible(false)
    materialForm.resetFields()
    setSelectedMaterial(null)
    message.success('材料保存成功')
  }

  const handleImportMaterials = () => {
    setIsImportModalVisible(true)
  }

  const handleExportMaterials = () => {
    message.success('材料库导出成功')
  }

  // 过滤材料
  const filteredMaterials = materials.filter(material => {
    const matchesSearch = material.name.toLowerCase().includes(searchText.toLowerCase()) ||
                         material.brand.toLowerCase().includes(searchText.toLowerCase()) ||
                         material.model.toLowerCase().includes(searchText.toLowerCase())
    const matchesCategory = !categoryFilter || material.category === categoryFilter
    return matchesSearch && matchesCategory
  })

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>材料管理</Title>
        <Paragraph>
          管理实训项目材料库，与3D编辑器联动实现自动成本计算
        </Paragraph>
      </div>

      {/* 统计数据 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="材料总数"
              value={materials.length}
              prefix={<InboxOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总库存价值"
              value={materials.reduce((sum, m) => sum + (m.stockQty * m.unitPrice), 0)}
              prefix="¥"
              precision={2}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="本月使用成本"
              value={materialUsages.reduce((sum, u) => sum + u.totalPrice, 0)}
              prefix="¥"
              precision={2}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="低库存材料"
              value={materials.filter(m => m.stockQty <= 10).length}
              prefix={<ShoppingCartOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="材料库" key="materials">
          <Card
            title="材料库管理"
            extra={
              <Space>
                <Input
                  placeholder="搜索材料名称、品牌或型号"
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  style={{ width: 250 }}
                  allowClear
                />
                <Select
                  placeholder="材料分类"
                  value={categoryFilter}
                  onChange={setCategoryFilter}
                  style={{ width: 120 }}
                  allowClear
                >
                  <Option value="structure">结构件</Option>
                  <Option value="decoration">装饰材料</Option>
                  <Option value="lighting">照明设备</Option>
                  <Option value="furniture">展具家具</Option>
                  <Option value="fabric">布艺材料</Option>
                  <Option value="electronic">电子设备</Option>
                </Select>
                <Button icon={<UploadOutlined />} onClick={handleImportMaterials}>
                  导入
                </Button>
                <Button icon={<DownloadOutlined />} onClick={handleExportMaterials}>
                  导出
                </Button>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />}
                  onClick={() => setIsMaterialModalVisible(true)}
                >
                  添加材料
                </Button>
              </Space>
            }
          >
            <Table
              columns={materialColumns}
              dataSource={filteredMaterials}
              rowKey="id"
              pagination={{ 
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 种材料`
              }}
            />
          </Card>
        </TabPane>

        <TabPane tab="使用记录" key="usage">
          <Card title="3D编辑器材料使用记录">
            <Table
              columns={usageColumns}
              dataSource={materialUsages}
              rowKey={(record) => `${record.materialId}-${record.editorSessionId}`}
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>

        <TabPane tab="成本分析" key="analysis">
          <Row gutter={16}>
            <Col span={12}>
              <Card title="材料使用排行">
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                  <DollarOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
                  <div style={{ marginTop: 16, color: '#999' }}>材料使用成本分析图表</div>
                </div>
              </Card>
            </Col>
            <Col span={12}>
              <Card title="成本趋势">
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                  <DollarOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
                  <div style={{ marginTop: 16, color: '#999' }}>成本趋势分析图表</div>
                </div>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>

      {/* 材料详情/编辑模态框 */}
      <Modal
        title={selectedMaterial ? '编辑材料' : '添加材料'}
        open={isMaterialModalVisible}
        onOk={() => materialForm.submit()}
        onCancel={() => {
          setIsMaterialModalVisible(false)
          materialForm.resetFields()
          setSelectedMaterial(null)
        }}
        width={800}
      >
        <Form
          form={materialForm}
          layout="vertical"
          onFinish={handleCreateMaterial}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="name" label="材料名称" rules={[{ required: true }]}>
                <Input placeholder="请输入材料名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="category" label="材料分类" rules={[{ required: true }]}>
                <Select placeholder="请选择分类">
                  <Option value="structure">结构件</Option>
                  <Option value="decoration">装饰材料</Option>
                  <Option value="lighting">照明设备</Option>
                  <Option value="furniture">展具家具</Option>
                  <Option value="fabric">布艺材料</Option>
                  <Option value="electronic">电子设备</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="brand" label="品牌" rules={[{ required: true }]}>
                <Input placeholder="请输入品牌" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="model" label="型号" rules={[{ required: true }]}>
                <Input placeholder="请输入型号" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="specification" label="规格" rules={[{ required: true }]}>
                <Input placeholder="请输入规格" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="unitPrice" label="单价" rules={[{ required: true }]}>
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入单价"
                  precision={2}
                  min={0}
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="unit" label="单位" rules={[{ required: true }]}>
                <Select placeholder="请选择单位">
                  <Option value="piece">件</Option>
                  <Option value="meter">米</Option>
                  <Option value="square_meter">平方米</Option>
                  <Option value="cubic_meter">立方米</Option>
                  <Option value="kilogram">公斤</Option>
                  <Option value="set">套</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="stockQty" label="库存数量" rules={[{ required: true }]}>
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入库存数量"
                  min={0}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="supplier" label="供应商" rules={[{ required: true }]}>
                <Input placeholder="请输入供应商名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="supplierContact" label="联系方式">
                <Input placeholder="请输入联系方式" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="description" label="材料描述">
            <TextArea rows={3} placeholder="请输入材料描述" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 导入材料模态框 */}
      <Modal
        title="批量导入材料"
        open={isImportModalVisible}
        onCancel={() => setIsImportModalVisible(false)}
        footer={null}
      >
        <Dragger>
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持Excel格式文件，请按照模板格式填写材料信息
          </p>
        </Dragger>
        <div style={{ marginTop: 16, textAlign: 'center' }}>
          <Button type="link">下载导入模板</Button>
        </div>
      </Modal>
    </div>
  )
}

export default MaterialManagePage
