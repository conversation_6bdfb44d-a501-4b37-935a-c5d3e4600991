import React, { useState } from 'react'
import { Layout, Menu, Button, Avatar, Dropdown, message } from 'antd'
import { useNavigate, useLocation } from 'react-router-dom'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  HomeOutlined,
  AppstoreOutlined,
  BookOutlined,
  ProjectOutlined,
  BulbOutlined,
  PictureOutlined,
  BoxPlotOutlined,
  EyeOutlined,
  CloudOutlined,
  SettingOutlined,
  UserOutlined,
  LogoutOutlined,
} from '@ant-design/icons'
import type { MenuProps } from 'antd'
import { useAuthStore } from '@/store/authStore'
import { ROUTES } from '@/utils/constants'

const { Header, Sider, Content } = Layout

interface MainLayoutProps {
  children: React.ReactNode
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()
  const { user, logout } = useAuthStore()

  const menuItems: MenuProps['items'] = [
    {
      key: ROUTES.HOME,
      icon: <HomeOutlined />,
      label: '首页',
    },
    {
      key: ROUTES.PORTAL,
      icon: <AppstoreOutlined />,
      label: '统一门户',
    },
    {
      key: 'learning',
      icon: <BookOutlined />,
      label: '教管学考',
      children: user?.role === 'teacher' ? [
        {
          key: ROUTES.LEARNING_CLASSES,
          label: '班级管理',
        },
        {
          key: '/learning/teacher/courses',
          label: '课程管理',
        },
        {
          key: '/learning/teacher/assignments',
          label: '作业管理',
        },
        {
          key: '/learning/teacher/exams',
          label: '考试中心',
        },
        {
          key: '/learning/teacher/questions',
          label: '题库管理',
        },
        {
          key: '/learning/teacher/resources',
          label: '资源库',
        },
        {
          key: '/learning/teacher/analytics',
          label: '学情分析',
        },
      ] : [
        {
          key: ROUTES.LEARNING,
          label: '学习中心',
        },
        {
          key: ROUTES.LEARNING_COURSES,
          label: '课程学习',
        },
        {
          key: ROUTES.LEARNING_ASSIGNMENTS,
          label: '作业中心',
        },
        {
          key: ROUTES.LEARNING_EXAMS,
          label: '考试中心',
        },
      ],
    },
    {
      key: 'projects',
      icon: <ProjectOutlined />,
      label: '项目管理',
      children: [
        {
          key: ROUTES.PROJECTS,
          label: '项目总览',
        },
        {
          key: ROUTES.PROJECT_MANAGEMENT,
          label: '项目管理',
        },
        {
          key: ROUTES.PROJECT_PROPOSAL,
          label: '方案管理',
        },
      ],
    },
    {
      key: 'design',
      icon: <BulbOutlined />,
      label: '设计工具',
      children: [
        {
          key: ROUTES.AI_PLAN,
          label: 'AI方案生成',
        },
        {
          key: ROUTES.RENDER_2D,
          label: '2D效果图',
        },
        {
          key: ROUTES.MODEL_3D,
          label: '3D模型',
        },
      ],
    },
    {
      key: ROUTES.VR_EDITOR,
      icon: <EyeOutlined />,
      label: 'VR编辑器',
    },
    {
      key: ROUTES.CONTENT_SPACE,
      icon: <CloudOutlined />,
      label: '内容空间',
    },
  ]

  // 根据用户角色过滤菜单项
  const getFilteredMenuItems = () => {
    const items = [...menuItems]

    // 如果是管理员，添加系统管理菜单
    if (user?.role === 'admin') {
      items.push({
        key: ROUTES.ADMIN,
        icon: <SettingOutlined />,
        label: '系统管理',
      })
    }

    return items
  }

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key)
  }

  const handleUserMenuClick = async ({ key }: { key: string }) => {
    if (key === 'logout') {
      await logout()
      message.success('退出登录成功')
      navigate(ROUTES.LOGIN)
    } else if (key === 'profile') {
      navigate(ROUTES.PROFILE)
    }
  }

  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人中心',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ]

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider trigger={null} collapsible collapsed={collapsed}>
        <div style={{ 
          height: 32, 
          margin: 16, 
          background: 'rgba(255, 255, 255, 0.3)',
          borderRadius: 6,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontWeight: 'bold'
        }}>
          {collapsed ? 'MCTT' : '会展实训平台'}
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={getFilteredMenuItems()}
          onClick={handleMenuClick}
        />
      </Sider>
      <Layout>
        <Header style={{ 
          padding: 0, 
          background: '#fff',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          boxShadow: '0 1px 4px rgba(0,21,41,.08)'
        }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{
              fontSize: '16px',
              width: 64,
              height: 64,
            }}
          />
          <div style={{ marginRight: 24, display: 'flex', alignItems: 'center', gap: 12 }}>
            <span style={{ color: '#666' }}>
              欢迎，{user?.username}
            </span>
            <Dropdown
              menu={{ items: userMenuItems, onClick: handleUserMenuClick }}
              placement="bottomRight"
            >
              <Avatar
                style={{ backgroundColor: '#1890ff', cursor: 'pointer' }}
                icon={<UserOutlined />}
              />
            </Dropdown>
          </div>
        </Header>
        <Content style={{ 
          margin: '24px 16px',
          padding: 24,
          minHeight: 280,
          background: '#fff',
          borderRadius: 6
        }}>
          {children}
        </Content>
      </Layout>
    </Layout>
  )
}

export default MainLayout
