import React, { useState, useEffect } from 'react'
import { Card, Row, Col, <PERSON>po<PERSON>, Button, Table, Tag, Space, Modal, Form, Input, Select, DatePicker, Tabs, Statistic, Upload, message, Progress } from 'antd'
import { useNavigate, useLocation } from 'react-router-dom'
import {
  BookOutlined,
  PlusOutlined,
  EditOutlined,
  EyeOutlined,
  TeamOutlined,
  FileTextOutlined,
  UploadOutlined,
  DownloadOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  UserOutlined,
  BarChartOutlined,
  TrophyOutlined,
} from '@ant-design/icons'
import { useAuthStore } from '@/store/authStore'
import type { ColumnsType } from 'antd/es/table'

const { Title, Paragraph, Text } = Typography
const { Option } = Select
const { TextArea } = Input
const { TabPane } = Tabs

interface Class {
  id: string
  name: string
  studentCount: number
  createdAt: string
  description: string
}

interface Course {
  id: string
  title: string
  classId: string
  className: string
  totalLessons: number
  completedLessons: number
  studentCount: number
  avgProgress: number
  status: 'draft' | 'published' | 'completed'
}

interface Assignment {
  id: string
  title: string
  courseId: string
  courseName: string
  className: string
  type: 'theory' | 'practice'
  dueDate: string
  submittedCount: number
  totalCount: number
  avgScore?: number
  status: 'active' | 'closed'
}

interface Student {
  id: string
  name: string
  studentId: string
  classId: string
  avgScore: number
  completedAssignments: number
  totalAssignments: number
  lastActive: string
}

const TeacherManagePage: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { user } = useAuthStore()
  const [activeTab, setActiveTab] = useState('classes')
  const [isClassModalVisible, setIsClassModalVisible] = useState(false)
  const [isCourseModalVisible, setIsCourseModalVisible] = useState(false)
  const [isAssignmentModalVisible, setIsAssignmentModalVisible] = useState(false)
  const [classForm] = Form.useForm()
  const [courseForm] = Form.useForm()
  const [assignmentForm] = Form.useForm()

  // 根据路由设置活动标签页
  useEffect(() => {
    const path = location.pathname
    if (path.includes('/teacher/courses')) {
      setActiveTab('courses')
    } else if (path.includes('/teacher/assignments')) {
      setActiveTab('assignments')
    } else if (path.includes('/teacher/analytics')) {
      setActiveTab('analytics')
    } else {
      setActiveTab('classes')
    }
  }, [location.pathname])

  // 模拟数据
  const [classes] = useState<Class[]>([
    { id: '1', name: '会展设计2024-1班', studentCount: 32, createdAt: '2024-01-15', description: '会展设计专业一班' },
    { id: '2', name: '会展设计2024-2班', studentCount: 28, createdAt: '2024-01-15', description: '会展设计专业二班' }
  ])

  const [courses] = useState<Course[]>([
    {
      id: '1',
      title: '会展策划基础',
      classId: '1',
      className: '会展设计2024-1班',
      totalLessons: 12,
      completedLessons: 8,
      studentCount: 32,
      avgProgress: 75,
      status: 'published'
    },
    {
      id: '2',
      title: '展示设计原理',
      classId: '2',
      className: '会展设计2024-2班',
      totalLessons: 10,
      completedLessons: 6,
      studentCount: 28,
      avgProgress: 60,
      status: 'published'
    }
  ])

  const [assignments] = useState<Assignment[]>([
    {
      id: '1',
      title: '展位设计方案',
      courseId: '1',
      courseName: '会展策划基础',
      className: '会展设计2024-1班',
      type: 'practice',
      dueDate: '2024-01-25',
      submittedCount: 25,
      totalCount: 32,
      avgScore: 82.5,
      status: 'active'
    },
    {
      id: '2',
      title: '理论知识测试',
      courseId: '1',
      courseName: '会展策划基础',
      className: '会展设计2024-1班',
      type: 'theory',
      dueDate: '2024-01-22',
      submittedCount: 32,
      totalCount: 32,
      avgScore: 85.2,
      status: 'closed'
    }
  ])

  const [students] = useState<Student[]>([
    {
      id: '1',
      name: '张三',
      studentId: '2024001',
      classId: '1',
      avgScore: 88.5,
      completedAssignments: 8,
      totalAssignments: 10,
      lastActive: '2024-01-21'
    },
    {
      id: '2',
      name: '李四',
      studentId: '2024002',
      classId: '1',
      avgScore: 76.3,
      completedAssignments: 7,
      totalAssignments: 10,
      lastActive: '2024-01-20'
    }
  ])

  const classColumns: ColumnsType<Class> = [
    { title: '班级名称', dataIndex: 'name', key: 'name' },
    { title: '学生人数', dataIndex: 'studentCount', key: 'studentCount' },
    { title: '创建时间', dataIndex: 'createdAt', key: 'createdAt' },
    { title: '描述', dataIndex: 'description', key: 'description' },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button type="link" icon={<EyeOutlined />}>查看</Button>
          <Button type="link" icon={<EditOutlined />}>编辑</Button>
          <Button type="link" icon={<TeamOutlined />}>管理学生</Button>
        </Space>
      )
    }
  ]

  const courseColumns: ColumnsType<Course> = [
    { title: '课程名称', dataIndex: 'title', key: 'title' },
    { title: '所属班级', dataIndex: 'className', key: 'className' },
    {
      title: '进度',
      key: 'progress',
      render: (_, record) => (
        <div>
          <Text>{record.completedLessons}/{record.totalLessons} 课时</Text>
          <Progress percent={record.avgProgress} size="small" style={{ marginTop: 4 }} />
        </div>
      )
    },
    { title: '学生数', dataIndex: 'studentCount', key: 'studentCount' },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'published' ? 'green' : status === 'draft' ? 'orange' : 'blue'}>
          {status === 'published' ? '已发布' : status === 'draft' ? '草稿' : '已完成'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button type="link" icon={<EyeOutlined />}>查看</Button>
          <Button type="link" icon={<EditOutlined />}>编辑</Button>
          <Button type="link" icon={<FileTextOutlined />}>布置作业</Button>
        </Space>
      )
    }
  ]

  const assignmentColumns: ColumnsType<Assignment> = [
    { title: '作业名称', dataIndex: 'title', key: 'title' },
    { title: '课程', dataIndex: 'courseName', key: 'courseName' },
    { title: '班级', dataIndex: 'className', key: 'className' },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <Tag color={type === 'practice' ? 'blue' : 'green'}>
          {type === 'practice' ? '实操作业' : '理论测试'}
        </Tag>
      )
    },
    {
      title: '提交情况',
      key: 'submission',
      render: (_, record) => (
        <div>
          <Text>{record.submittedCount}/{record.totalCount}</Text>
          <Progress 
            percent={Math.round((record.submittedCount / record.totalCount) * 100)} 
            size="small" 
            style={{ marginTop: 4 }}
          />
        </div>
      )
    },
    {
      title: '平均分',
      dataIndex: 'avgScore',
      key: 'avgScore',
      render: (score) => score ? `${score.toFixed(1)}分` : '-'
    },
    { title: '截止时间', dataIndex: 'dueDate', key: 'dueDate' },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button type="link" icon={<EyeOutlined />}>查看详情</Button>
          <Button type="link" icon={<BarChartOutlined />}>统计分析</Button>
          <Button type="link">批改</Button>
        </Space>
      )
    }
  ]

  const handleCreateClass = async (values: any) => {
    console.log('创建班级:', values)
    setIsClassModalVisible(false)
    classForm.resetFields()
    message.success('班级创建成功')
  }

  const handleCreateCourse = async (values: any) => {
    console.log('创建课程:', values)
    setIsCourseModalVisible(false)
    courseForm.resetFields()
    message.success('课程创建成功')
  }

  const handleCreateAssignment = async (values: any) => {
    console.log('布置作业:', values)
    setIsAssignmentModalVisible(false)
    assignmentForm.resetFields()
    message.success('作业布置成功')
  }

  const handleTabChange = (key: string) => {
    setActiveTab(key)
    // 根据标签页更新URL
    switch (key) {
      case 'classes':
        navigate('/learning/classes')
        break
      case 'courses':
        navigate('/learning/teacher/courses')
        break
      case 'assignments':
        navigate('/learning/teacher/assignments')
        break
      case 'analytics':
        navigate('/learning/teacher/analytics')
        break
      default:
        navigate('/learning/classes')
    }
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>教学管理中心</Title>
        <Paragraph>
          课程管理、作业布置、学情分析一体化平台
        </Paragraph>
      </div>

      {/* 统计数据 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="我的班级"
              value={classes.length}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="进行中课程"
              value={courses.filter(c => c.status === 'published').length}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待批改作业"
              value={assignments.filter(a => a.submittedCount < a.totalCount).length}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="学生总数"
              value={classes.reduce((sum, cls) => sum + cls.studentCount, 0)}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        <TabPane tab="我的班级" key="classes">
          <Card
            title="班级管理"
            extra={
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => setIsClassModalVisible(true)}
              >
                创建班级
              </Button>
            }
          >
            <Table
              columns={classColumns}
              dataSource={classes}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>

        <TabPane tab="我的课程" key="courses">
          <Card
            title="课程管理"
            extra={
              <Space>
                <Button icon={<UploadOutlined />}>导入课程</Button>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />}
                  onClick={() => setIsCourseModalVisible(true)}
                >
                  创建课程
                </Button>
              </Space>
            }
          >
            <Table
              columns={courseColumns}
              dataSource={courses}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>

        <TabPane tab="作业管理" key="assignments">
          <Card
            title="作业与考试"
            extra={
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => setIsAssignmentModalVisible(true)}
              >
                布置作业
              </Button>
            }
          >
            <Table
              columns={assignmentColumns}
              dataSource={assignments}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>

        <TabPane tab="学情分析" key="analytics">
          <Row gutter={16}>
            <Col span={12}>
              <Card title="班级学习情况" style={{ marginBottom: 16 }}>
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                  <BarChartOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
                  <div style={{ marginTop: 16, color: '#999' }}>学习数据分析图表</div>
                </div>
              </Card>
            </Col>
            <Col span={12}>
              <Card title="作业完成统计" style={{ marginBottom: 16 }}>
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                  <TrophyOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
                  <div style={{ marginTop: 16, color: '#999' }}>作业统计图表</div>
                </div>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>

      {/* 创建班级模态框 */}
      <Modal
        title="创建班级"
        open={isClassModalVisible}
        onOk={() => classForm.submit()}
        onCancel={() => setIsClassModalVisible(false)}
      >
        <Form form={classForm} layout="vertical" onFinish={handleCreateClass}>
          <Form.Item name="name" label="班级名称" rules={[{ required: true }]}>
            <Input placeholder="请输入班级名称" />
          </Form.Item>
          <Form.Item name="description" label="班级描述">
            <TextArea rows={3} placeholder="请输入班级描述" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 创建课程模态框 */}
      <Modal
        title="创建课程"
        open={isCourseModalVisible}
        onOk={() => courseForm.submit()}
        onCancel={() => setIsCourseModalVisible(false)}
        width={600}
      >
        <Form form={courseForm} layout="vertical" onFinish={handleCreateCourse}>
          <Form.Item name="title" label="课程名称" rules={[{ required: true }]}>
            <Input placeholder="请输入课程名称" />
          </Form.Item>
          <Form.Item name="classId" label="所属班级" rules={[{ required: true }]}>
            <Select placeholder="请选择班级">
              {classes.map(cls => (
                <Option key={cls.id} value={cls.id}>{cls.name}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="description" label="课程描述">
            <TextArea rows={3} placeholder="请输入课程描述" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 布置作业模态框 */}
      <Modal
        title="布置作业"
        open={isAssignmentModalVisible}
        onOk={() => assignmentForm.submit()}
        onCancel={() => setIsAssignmentModalVisible(false)}
        width={600}
      >
        <Form form={assignmentForm} layout="vertical" onFinish={handleCreateAssignment}>
          <Form.Item name="title" label="作业标题" rules={[{ required: true }]}>
            <Input placeholder="请输入作业标题" />
          </Form.Item>
          <Form.Item name="courseId" label="所属课程" rules={[{ required: true }]}>
            <Select placeholder="请选择课程">
              {courses.map(course => (
                <Option key={course.id} value={course.id}>{course.title}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="type" label="作业类型" rules={[{ required: true }]}>
            <Select placeholder="请选择作业类型">
              <Option value="theory">理论知识</Option>
              <Option value="practice">实操知识</Option>
            </Select>
          </Form.Item>
          <Form.Item name="dueDate" label="截止时间" rules={[{ required: true }]}>
            <DatePicker showTime style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item name="description" label="作业要求" rules={[{ required: true }]}>
            <TextArea rows={4} placeholder="请详细描述作业要求..." />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default TeacherManagePage
