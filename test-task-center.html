<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务中心测试页面</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/antd@5.12.8/dist/reset.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 24px;
        }
        .task-center-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .page-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #262626;
        }
        .page-description {
            color: #8c8c8c;
            margin-bottom: 24px;
        }
        .card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
            padding: 24px;
            margin-bottom: 24px;
        }
        .steps-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            position: relative;
        }
        .step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 16px;
            right: -50%;
            width: 100%;
            height: 1px;
            background: #d9d9d9;
            z-index: 1;
        }
        .step-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            color: #8c8c8c;
            z-index: 2;
            position: relative;
            background: white;
            border: 1px solid #d9d9d9;
        }
        .step-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
        }
        .step-description {
            font-size: 12px;
            color: #8c8c8c;
            text-align: center;
        }
        .placeholder-content {
            text-align: center;
            padding: 40px 0;
            color: #8c8c8c;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        .feature-item {
            padding: 16px;
            border: 1px solid #f0f0f0;
            border-radius: 6px;
        }
        .feature-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }
        .feature-icon {
            margin-right: 8px;
            width: 16px;
            height: 16px;
        }
        .feature-description {
            color: #8c8c8c;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="task-center-container">
        <!-- 页面标题 -->
        <div style="margin-bottom: 24px;">
            <h1 class="page-title">任务中心</h1>
            <p class="page-description">
                基于六步教学法的任务管理中心，引导完整的学习过程
            </p>
        </div>

        <!-- 六步教学法指引 -->
        <div class="card">
            <div class="steps-container">
                <div class="step">
                    <div class="step-icon">🔍</div>
                    <div class="step-title">资讯</div>
                    <div class="step-description">获取信息，明确任务</div>
                </div>
                <div class="step">
                    <div class="step-icon">📋</div>
                    <div class="step-title">计划</div>
                    <div class="step-description">制定工作计划</div>
                </div>
                <div class="step">
                    <div class="step-icon">💡</div>
                    <div class="step-title">决策</div>
                    <div class="step-description">选择最佳方案</div>
                </div>
                <div class="step">
                    <div class="step-icon">🔧</div>
                    <div class="step-title">实施</div>
                    <div class="step-description">执行工作任务</div>
                </div>
                <div class="step">
                    <div class="step-icon">✅</div>
                    <div class="step-title">检查</div>
                    <div class="step-description">检验工作成果</div>
                </div>
                <div class="step">
                    <div class="step-icon">🎯</div>
                    <div class="step-title">评估</div>
                    <div class="step-description">总结反思提升</div>
                </div>
            </div>
        </div>

        <!-- 任务列表 -->
        <div class="card">
            <h3 style="margin-bottom: 16px;">可用任务</h3>
            <div class="placeholder-content">
                <div style="font-size: 16px; margin-bottom: 16px;">任务列表功能开发中...</div>
                <div style="font-size: 14px;">
                    完整的任务管理功能将包括任务发布、分析引导、进度跟踪等
                </div>
            </div>
        </div>

        <!-- 功能说明 -->
        <div class="card">
            <h3 style="margin-bottom: 16px;">六步教学法说明</h3>
            <div class="feature-grid">
                <div class="feature-item">
                    <div class="feature-title">
                        <span class="feature-icon" style="color: #1890ff;">🔍</span>
                        第一步：资讯
                    </div>
                    <div class="feature-description">
                        获取任务信息，明确目标要求，收集相关资料和案例
                    </div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-title">
                        <span class="feature-icon" style="color: #52c41a;">📋</span>
                        第二步：计划
                    </div>
                    <div class="feature-description">
                        制定工作计划，分配时间和资源，设定里程碑
                    </div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-title">
                        <span class="feature-icon" style="color: #faad14;">💡</span>
                        第三步：决策
                    </div>
                    <div class="feature-description">
                        比较多个方案，选择最佳解决方案，确定实施路径
                    </div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-title">
                        <span class="feature-icon" style="color: #722ed1;">🔧</span>
                        第四步：实施
                    </div>
                    <div class="feature-description">
                        执行工作任务，使用3D编辑器进行设计实现
                    </div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-title">
                        <span class="feature-icon" style="color: #13c2c2;">✅</span>
                        第五步：检查
                    </div>
                    <div class="feature-description">
                        检验工作成果，对照标准进行质量控制
                    </div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-title">
                        <span class="feature-icon" style="color: #f5222d;">🎯</span>
                        第六步：评估
                    </div>
                    <div class="feature-description">
                        总结反思提升，记录经验教训，制定改进计划
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试说明 */
        <div class="card">
            <h3 style="margin-bottom: 16px;">页面测试说明</h3>
            <div style="color: #52c41a; margin-bottom: 16px;">
                ✅ 任务中心页面已成功创建并可以正常显示！
            </div>
            <div style="margin-bottom: 12px;">
                <strong>已实现的功能：</strong>
            </div>
            <ul style="margin-left: 20px; color: #666;">
                <li>六步教学法流程展示</li>
                <li>任务列表区域（开发中）</li>
                <li>功能说明和介绍</li>
                <li>响应式布局设计</li>
            </ul>
            <div style="margin-top: 16px; padding: 12px; background: #f6ffed; border: 1px solid #b7eb8f; border-radius: 6px;">
                <strong>下一步：</strong> 当开发服务器正常启动后，这个页面将集成到完整的React应用中，并添加更多交互功能。
            </div>
        </div>
    </div>
</body>
</html>
