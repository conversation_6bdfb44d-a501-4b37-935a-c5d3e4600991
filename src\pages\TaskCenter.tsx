import React from 'react'
import { Card, Typography, Steps } from 'antd'
import {
  CheckCircleOutlined,
  FileTextOutlined,
  BulbOutlined,
  ToolOutlined,
  SearchOutlined,
  TargetOutlined,
} from '@ant-design/icons'

const { Title, Paragraph, Text } = Typography

const TaskCenter: React.FC = () => {
  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>任务中心</Title>
        <Paragraph>
          基于六步教学法的任务管理中心，引导完整的学习过程
        </Paragraph>
      </div>

      {/* 六步教学法指引 */}
      <Card style={{ marginBottom: 24 }}>
        <Steps 
          current={-1} 
          size="small"
          items={[
            { title: "资讯", description: "获取信息，明确任务", icon: <SearchOutlined /> },
            { title: "计划", description: "制定工作计划", icon: <FileTextOutlined /> },
            { title: "决策", description: "选择最佳方案", icon: <BulbOutlined /> },
            { title: "实施", description: "执行工作任务", icon: <ToolOutlined /> },
            { title: "检查", description: "检验工作成果", icon: <CheckCircleOutlined /> },
            { title: "评估", description: "总结反思提升", icon: <TargetOutlined /> }
          ]}
        />
      </Card>

      {/* 任务列表 */}
      <Card title="可用任务">
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Text>任务列表功能开发中...</Text>
          <div style={{ marginTop: 16 }}>
            <Text type="secondary">
              完整的任务管理功能将包括任务发布、分析引导、进度跟踪等
            </Text>
          </div>
        </div>
      </Card>

      {/* 功能说明 */}
      <Card title="六步教学法说明" style={{ marginTop: 24 }}>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '16px' }}>
          <div>
            <Title level={5}>
              <SearchOutlined style={{ color: '#1890ff', marginRight: 8 }} />
              第一步：资讯
            </Title>
            <Text type="secondary">
              获取任务信息，明确目标要求，收集相关资料和案例
            </Text>
          </div>
          
          <div>
            <Title level={5}>
              <FileTextOutlined style={{ color: '#52c41a', marginRight: 8 }} />
              第二步：计划
            </Title>
            <Text type="secondary">
              制定工作计划，分配时间和资源，设定里程碑
            </Text>
          </div>
          
          <div>
            <Title level={5}>
              <BulbOutlined style={{ color: '#faad14', marginRight: 8 }} />
              第三步：决策
            </Title>
            <Text type="secondary">
              比较多个方案，选择最佳解决方案，确定实施路径
            </Text>
          </div>
          
          <div>
            <Title level={5}>
              <ToolOutlined style={{ color: '#722ed1', marginRight: 8 }} />
              第四步：实施
            </Title>
            <Text type="secondary">
              执行工作任务，使用3D编辑器进行设计实现
            </Text>
          </div>
          
          <div>
            <Title level={5}>
              <CheckCircleOutlined style={{ color: '#13c2c2', marginRight: 8 }} />
              第五步：检查
            </Title>
            <Text type="secondary">
              检验工作成果，对照标准进行质量控制
            </Text>
          </div>
          
          <div>
            <Title level={5}>
              <TargetOutlined style={{ color: '#f5222d', marginRight: 8 }} />
              第六步：评估
            </Title>
            <Text type="secondary">
              总结反思提升，记录经验教训，制定改进计划
            </Text>
          </div>
        </div>
      </Card>
    </div>
  )
}

export default TaskCenter
