import React, { useState, useEffect } from 'react'
import { Card, Row, Col, Typo<PERSON>, Button, Table, Tag, Space, Modal, Form, Input, Select, DatePicker, Tabs, Statistic } from 'antd'
import { useNavigate } from 'react-router-dom'
import {
  ProjectOutlined,
  PlusOutlined,
  EditOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
  CalendarOutlined,
  FileTextOutlined,
} from '@ant-design/icons'
import { useAuthStore } from '@/store/authStore'
import { ROUTES } from '@/utils/constants'
import type { ColumnsType } from 'antd/es/table'

const { Title, Paragraph, Text } = Typography
const { Option } = Select
const { TextArea } = Input
const { TabPane } = Tabs

interface ProjectItem {
  id: string
  name: string
  description: string
  type: 'exhibition' | 'conference' | 'trade_show'
  status: 'planning' | 'design' | 'construction' | 'completed' | 'cancelled'
  priority: 'high' | 'medium' | 'low'
  createdBy: string
  assignedTo: string[]
  startDate: string
  endDate: string
  progress: number
  budget?: number
  location?: string
}

interface Proposal {
  id: string
  projectName: string
  submittedBy: string
  submittedAt: string
  status: 'pending' | 'approved' | 'rejected'
  description: string
  estimatedBudget: number
  estimatedDuration: number
}

const ProjectManagementPage: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAuthStore()
  const [activeTab, setActiveTab] = useState('overview')
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false)
  const [isProposalModalVisible, setIsProposalModalVisible] = useState(false)
  const [form] = Form.useForm()
  const [proposalForm] = Form.useForm()

  // 模拟项目数据
  const [projects] = useState<ProjectItem[]>([
    {
      id: '1',
      name: '2024春季汽车展',
      description: '大型汽车展览会，展示最新汽车技术和产品',
      type: 'exhibition',
      status: 'design',
      priority: 'high',
      createdBy: '张老师',
      assignedTo: ['学生A', '学生B'],
      startDate: '2024-02-01',
      endDate: '2024-02-15',
      progress: 65,
      budget: 500000,
      location: '国际会展中心'
    },
    {
      id: '2',
      name: '科技创新大会',
      description: '展示最新科技创新成果的会议展览',
      type: 'conference',
      status: 'planning',
      priority: 'medium',
      createdBy: '李老师',
      assignedTo: ['学生C', '学生D'],
      startDate: '2024-03-01',
      endDate: '2024-03-10',
      progress: 30,
      budget: 300000,
      location: '科技园会议中心'
    }
  ])

  const [proposals] = useState<Proposal[]>([
    {
      id: '1',
      projectName: '绿色环保主题展',
      submittedBy: '学生E',
      submittedAt: '2024-01-20',
      status: 'pending',
      description: '以环保为主题的展览活动，展示绿色科技和环保产品',
      estimatedBudget: 200000,
      estimatedDuration: 10
    },
    {
      id: '2',
      projectName: '文化艺术节',
      submittedBy: '学生F',
      submittedAt: '2024-01-18',
      status: 'approved',
      description: '展示传统文化和现代艺术的综合性文化活动',
      estimatedBudget: 150000,
      estimatedDuration: 7
    }
  ])

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      planning: 'blue',
      design: 'orange',
      construction: 'purple',
      completed: 'green',
      cancelled: 'red',
      pending: 'gold',
      approved: 'green',
      rejected: 'red'
    }
    return colorMap[status] || 'default'
  }

  const getStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      planning: '规划中',
      design: '设计中',
      construction: '施工中',
      completed: '已完成',
      cancelled: '已取消',
      pending: '待审批',
      approved: '已通过',
      rejected: '已驳回'
    }
    return textMap[status] || status
  }

  const getPriorityColor = (priority: string) => {
    const colorMap: Record<string, string> = {
      high: 'red',
      medium: 'orange',
      low: 'green'
    }
    return colorMap[priority] || 'default'
  }

  const getPriorityText = (priority: string) => {
    const textMap: Record<string, string> = {
      high: '高',
      medium: '中',
      low: '低'
    }
    return textMap[priority] || priority
  }

  const projectColumns: ColumnsType<ProjectItem> = [
    {
      title: '项目名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space direction="vertical" size={0}>
          <Text strong>{text}</Text>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.description}
          </Text>
        </Space>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => {
        const typeMap: Record<string, string> = {
          exhibition: '展览',
          conference: '会议',
          trade_show: '贸易展'
        }
        return <Tag>{typeMap[type] || type}</Tag>
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority) => (
        <Tag color={getPriorityColor(priority)}>
          {getPriorityText(priority)}
        </Tag>
      )
    },
    {
      title: '负责人',
      dataIndex: 'assignedTo',
      key: 'assignedTo',
      render: (assignedTo) => (
        <Space>
          {assignedTo.map((person, index) => (
            <Tag key={index} icon={<UserOutlined />}>
              {person}
            </Tag>
          ))}
        </Space>
      )
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress) => `${progress}%`
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button 
            type="link" 
            icon={<EyeOutlined />}
            onClick={() => navigate(`${ROUTES.PROJECTS}/${record.id}`)}
          >
            查看
          </Button>
          <Button 
            type="link" 
            icon={<EditOutlined />}
            onClick={() => navigate(`${ROUTES.PROJECTS}/${record.id}/edit`)}
          >
            编辑
          </Button>
        </Space>
      )
    }
  ]

  const proposalColumns: ColumnsType<Proposal> = [
    {
      title: '项目名称',
      dataIndex: 'projectName',
      key: 'projectName'
    },
    {
      title: '申请人',
      dataIndex: 'submittedBy',
      key: 'submittedBy'
    },
    {
      title: '申请时间',
      dataIndex: 'submittedAt',
      key: 'submittedAt'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: '预算',
      dataIndex: 'estimatedBudget',
      key: 'estimatedBudget',
      render: (budget) => `¥${budget.toLocaleString()}`
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button type="link" icon={<EyeOutlined />}>
            查看详情
          </Button>
          {user?.role === 'teacher' && record.status === 'pending' && (
            <>
              <Button type="link" style={{ color: '#52c41a' }}>
                通过
              </Button>
              <Button type="link" danger>
                驳回
              </Button>
            </>
          )}
        </Space>
      )
    }
  ]

  const handleCreateProject = async (values: any) => {
    console.log('创建项目:', values)
    setIsCreateModalVisible(false)
    form.resetFields()
  }

  const handleSubmitProposal = async (values: any) => {
    console.log('提交方案:', values)
    setIsProposalModalVisible(false)
    proposalForm.resetFields()
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>项目管理平台</Title>
        <Paragraph>
          项目全生命周期管理，从立项到交付的完整流程
        </Paragraph>
      </div>

      {/* 统计数据 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="进行中项目"
              value={projects.filter(p => ['planning', 'design', 'construction'].includes(p.status)).length}
              prefix={<ProjectOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已完成项目"
              value={projects.filter(p => p.status === 'completed').length}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待审批方案"
              value={proposals.filter(p => p.status === 'pending').length}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="本月新增"
              value={2}
              prefix={<PlusOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="项目总览" key="overview">
          <Card
            title="我的项目"
            extra={
              <Space>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />}
                  onClick={() => setIsCreateModalVisible(true)}
                >
                  创建项目
                </Button>
                <Button 
                  icon={<FileTextOutlined />}
                  onClick={() => setIsProposalModalVisible(true)}
                >
                  提交方案
                </Button>
              </Space>
            }
          >
            <Table
              columns={projectColumns}
              dataSource={projects}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>

        <TabPane tab="方案管理" key="proposals">
          <Card title="项目方案">
            <Table
              columns={proposalColumns}
              dataSource={proposals}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>
      </Tabs>

      {/* 创建项目模态框 */}
      <Modal
        title="创建新项目"
        open={isCreateModalVisible}
        onOk={() => form.submit()}
        onCancel={() => setIsCreateModalVisible(false)}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateProject}
        >
          <Form.Item
            name="name"
            label="项目名称"
            rules={[{ required: true, message: '请输入项目名称' }]}
          >
            <Input placeholder="请输入项目名称" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="项目描述"
            rules={[{ required: true, message: '请输入项目描述' }]}
          >
            <TextArea rows={3} placeholder="请输入项目描述" />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="type"
                label="项目类型"
                rules={[{ required: true, message: '请选择项目类型' }]}
              >
                <Select placeholder="请选择项目类型">
                  <Option value="exhibition">展览</Option>
                  <Option value="conference">会议</Option>
                  <Option value="trade_show">贸易展</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="priority"
                label="优先级"
                rules={[{ required: true, message: '请选择优先级' }]}
              >
                <Select placeholder="请选择优先级">
                  <Option value="high">高</Option>
                  <Option value="medium">中</Option>
                  <Option value="low">低</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="startDate"
                label="开始日期"
                rules={[{ required: true, message: '请选择开始日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="endDate"
                label="结束日期"
                rules={[{ required: true, message: '请选择结束日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 提交方案模态框 */}
      <Modal
        title="提交项目方案"
        open={isProposalModalVisible}
        onOk={() => proposalForm.submit()}
        onCancel={() => setIsProposalModalVisible(false)}
        width={600}
      >
        <Form
          form={proposalForm}
          layout="vertical"
          onFinish={handleSubmitProposal}
        >
          <Form.Item
            name="projectName"
            label="项目名称"
            rules={[{ required: true, message: '请输入项目名称' }]}
          >
            <Input placeholder="请输入项目名称" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="项目描述"
            rules={[{ required: true, message: '请输入项目描述' }]}
          >
            <TextArea rows={4} placeholder="请详细描述项目内容、目标和预期效果" />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="estimatedBudget"
                label="预估预算（元）"
                rules={[{ required: true, message: '请输入预估预算' }]}
              >
                <Input type="number" placeholder="请输入预估预算" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="estimatedDuration"
                label="预估工期（天）"
                rules={[{ required: true, message: '请输入预估工期' }]}
              >
                <Input type="number" placeholder="请输入预估工期" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  )
}

export default ProjectManagementPage
